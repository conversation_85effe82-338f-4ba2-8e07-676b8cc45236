import { render } from '@testing-library/react'
import { GoogleTagManager, trackEvent, trackPurchase } from '@/components/analytics/google-tag-manager'

// Mock Next.js Script component
jest.mock('next/script', () => {
  return function MockScript({ children, dangerouslySetInnerHTML, ...props }: any) {
    if (dangerouslySetInnerHTML) {
      return <script {...props} dangerouslySetInnerHTML={dangerouslySetInnerHTML} />
    }
    return <script {...props}>{children}</script>
  }
})

describe('GoogleTagManager', () => {
  beforeEach(() => {
    // Reset window.dataLayer before each test
    delete (window as any).dataLayer
  })

  it('should render GTM script with correct ID', () => {
    const { container } = render(<GoogleTagManager gtmId="GTM-WBQDV2NR" />)
    
    const script = container.querySelector('script')
    expect(script).toBeInTheDocument()
    expect(script?.innerHTML).toContain('GTM-WBQDV2NR')
    expect(script?.innerHTML).toContain('googletagmanager.com/gtm.js')
  })

  it('should render noscript fallback', () => {
    const { container } = render(<GoogleTagManager gtmId="GTM-WBQDV2NR" />)

    const noscript = container.querySelector('noscript')
    expect(noscript).toBeInTheDocument()
  })

  it('should initialize dataLayer on mount', () => {
    render(<GoogleTagManager gtmId="GTM-WBQDV2NR" />)
    
    expect(window.dataLayer).toBeDefined()
    expect(Array.isArray(window.dataLayer)).toBe(true)
  })

  describe('Event Tracking', () => {
    beforeEach(() => {
      window.dataLayer = []
    })

    it('should track custom events', () => {
      trackEvent('test_event', { custom_parameter: 'test_value' })
      
      expect(window.dataLayer).toHaveLength(1)
      expect(window.dataLayer[0]).toEqual({
        event: 'test_event',
        custom_parameter: 'test_value'
      })
    })

    it('should track purchase events', () => {
      trackPurchase('ORDER-123', 99.99, 'CHF', [{ item_id: 'product-1' }])
      
      expect(window.dataLayer).toHaveLength(1)
      expect(window.dataLayer[0]).toEqual({
        event: 'purchase',
        transaction_id: 'ORDER-123',
        value: 99.99,
        currency: 'CHF',
        items: [{ item_id: 'product-1' }]
      })
    })

    it('should handle missing dataLayer gracefully', () => {
      delete (window as any).dataLayer
      
      expect(() => {
        trackEvent('test_event')
      }).not.toThrow()
    })
  })
})
